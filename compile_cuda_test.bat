@echo off
echo === PhotonRender CUDA Compilation Test ===

REM Change to project directory
cd /d "C:\xampp\htdocs\progetti\photon-render"

REM Setup Visual Studio environment
call "C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Auxiliary\Build\vcvars64.bat"

echo.
echo [INFO] Current directory: %CD%
echo [INFO] Compiling CUDA test...
nvcc -o test_cuda_simple.exe test_cuda_simple.cu -arch=sm_89

if %ERRORLEVEL% EQU 0 (
    echo [SUCCESS] CUDA compilation successful
    echo.
    echo [INFO] Running CUDA test...
    test_cuda_simple.exe
) else (
    echo [ERROR] CUDA compilation failed
    exit /b 1
)

echo.
echo [INFO] CUDA test completed
pause
