// test_cuda_simple.cu
// Test CUDA semplice per verificare che nvcc funzioni

#include <cuda_runtime.h>
#include <device_launch_parameters.h>
#include <stdio.h>
#include <math.h>

// Kernel CUDA semplice per test
__global__ void simple_test_kernel(float* output, int width, int height) {
    int x = blockIdx.x * blockDim.x + threadIdx.x;
    int y = blockIdx.y * blockDim.y + threadIdx.y;
    
    if (x >= width || y >= height) return;
    
    int idx = y * width + x;
    
    // Crea un pattern semplice
    float u = float(x) / float(width);
    float v = float(y) / float(height);
    
    output[idx] = u * v; // Gradiente semplice
}

int main() {
    printf("=== PhotonRender CUDA Simple Test ===\n");
    
    // Verifica device CUDA
    int device_count;
    cudaError_t error = cudaGetDeviceCount(&device_count);
    
    if (error != cudaSuccess || device_count == 0) {
        printf("[ERROR] No CUDA devices found\n");
        return 1;
    }
    
    printf("[INFO] Found %d CUDA device(s)\n", device_count);
    
    // Ottieni proprietà device
    cudaDeviceProp prop;
    cudaGetDeviceProperties(&prop, 0);
    
    printf("[INFO] Device: %s\n", prop.name);
    printf("[INFO] Compute Capability: %d.%d\n", prop.major, prop.minor);
    printf("[INFO] Global Memory: %.1f GB\n", prop.totalGlobalMem / (1024.0f * 1024.0f * 1024.0f));
    printf("[INFO] Max Threads per Block: %d\n", prop.maxThreadsPerBlock);
    
    // Test allocazione memoria
    const int width = 256;
    const int height = 256;
    const size_t size = width * height * sizeof(float);
    
    printf("\n[TEST] Testing memory allocation (%dx%d = %.1f MB)...\n", 
           width, height, size / (1024.0f * 1024.0f));
    
    float* h_data = (float*)malloc(size);
    float* d_data;
    
    error = cudaMalloc(&d_data, size);
    if (error != cudaSuccess) {
        printf("[ERROR] Failed to allocate GPU memory: %s\n", cudaGetErrorString(error));
        free(h_data);
        return 1;
    }
    
    printf("[SUCCESS] GPU memory allocated\n");
    
    // Test kernel launch
    printf("\n[TEST] Testing kernel launch...\n");
    
    dim3 block_size(16, 16);
    dim3 grid_size((width + block_size.x - 1) / block_size.x,
                   (height + block_size.y - 1) / block_size.y);
    
    printf("[INFO] Grid: %dx%d, Block: %dx%d\n", 
           grid_size.x, grid_size.y, block_size.x, block_size.y);
    
    // Avvia kernel
    simple_test_kernel<<<grid_size, block_size>>>(d_data, width, height);
    
    // Verifica errori kernel
    error = cudaGetLastError();
    if (error != cudaSuccess) {
        printf("[ERROR] Kernel launch failed: %s\n", cudaGetErrorString(error));
        cudaFree(d_data);
        free(h_data);
        return 1;
    }
    
    // Aspetta completamento
    error = cudaDeviceSynchronize();
    if (error != cudaSuccess) {
        printf("[ERROR] Device synchronization failed: %s\n", cudaGetErrorString(error));
        cudaFree(d_data);
        free(h_data);
        return 1;
    }
    
    printf("[SUCCESS] Kernel executed successfully\n");
    
    // Copia risultato su host
    printf("\n[TEST] Testing memory copy...\n");
    
    error = cudaMemcpy(h_data, d_data, size, cudaMemcpyDeviceToHost);
    if (error != cudaSuccess) {
        printf("[ERROR] Memory copy failed: %s\n", cudaGetErrorString(error));
        cudaFree(d_data);
        free(h_data);
        return 1;
    }
    
    printf("[SUCCESS] Memory copy completed\n");
    
    // Verifica risultati
    printf("\n[TEST] Verifying results...\n");
    
    bool results_ok = true;
    for (int y = 0; y < height && results_ok; y++) {
        for (int x = 0; x < width && results_ok; x++) {
            int idx = y * width + x;
            float expected = (float(x) / float(width)) * (float(y) / float(height));
            float actual = h_data[idx];
            
            if (fabsf(actual - expected) > 1e-6f) {
                printf("[ERROR] Mismatch at (%d,%d): expected %f, got %f\n", 
                       x, y, expected, actual);
                results_ok = false;
            }
        }
    }
    
    if (results_ok) {
        printf("[SUCCESS] All results verified correctly\n");
    }
    
    // Cleanup
    cudaFree(d_data);
    free(h_data);
    
    printf("\n=== CUDA Test Summary ===\n");
    if (results_ok) {
        printf("[SUCCESS] All CUDA tests passed!\n");
        printf("CUDA is working correctly on this system.\n");
        return 0;
    } else {
        printf("[FAILURE] Some tests failed.\n");
        return 1;
    }
}
