// src/main.cpp
// PhotonRender - Professional Rendering Engine for SketchUp
// Test application and example usage

#include <iostream>
#include <memory>
#include <chrono>

// PhotonRender headers
#include "../include/photon/photon.hpp"
#include "core/math/matrix4.hpp"
#include "core/renderer.hpp"
#include "core/scene/scene.hpp"
#include "core/scene/scene_loader.hpp"
#include "core/geometry/mesh_loader.hpp"
#include "core/test/mock_renderer.hpp"
#include "core/scene/camera.hpp"
#include "core/scene/light.hpp"
#include "core/integrator/integrator.hpp"
#include "core/material/material.hpp"
#include "core/sampler/sampler.hpp"

using namespace photon;

/**
 * @brief Create a simple Cornell Box scene for testing
 */
std::shared_ptr<Scene> createCornellBoxScene() {
    auto scene = std::make_shared<Scene>();
    
    // Create materials
    auto whiteMaterial = std::make_shared<DiffuseMaterial>(Color3(0.8f, 0.8f, 0.8f));
    auto redMaterial = std::make_shared<DiffuseMaterial>(Color3(0.8f, 0.1f, 0.1f));
    auto greenMaterial = std::make_shared<DiffuseMaterial>(Color3(0.1f, 0.8f, 0.1f));
    auto lightMaterial = std::make_shared<EmissiveMaterial>(Color3(15.0f, 15.0f, 15.0f));
    
    // Add materials to scene
    scene->addMaterial("white", whiteMaterial);
    scene->addMaterial("red", redMaterial);
    scene->addMaterial("green", greenMaterial);
    scene->addMaterial("light", lightMaterial);
    
    // Create Cornell Box geometry
    
    // Floor
    auto floor = std::make_shared<Mesh>("floor");
    floor->addQuad(
        Point3(-1.0f, 0.0f, -1.0f), Point3(1.0f, 0.0f, -1.0f),
        Point3(1.0f, 0.0f, 1.0f), Point3(-1.0f, 0.0f, 1.0f)
    );
    // TODO: Implement material assignment per mesh
    scene->addMesh(floor);
    
    // Ceiling
    auto ceiling = std::make_shared<Mesh>("ceiling");
    ceiling->addQuad(
        Point3(-1.0f, 2.0f, 1.0f), Point3(1.0f, 2.0f, 1.0f),
        Point3(1.0f, 2.0f, -1.0f), Point3(-1.0f, 2.0f, -1.0f)
    );
    // TODO: Implement material assignment per mesh
    scene->addMesh(ceiling);
    
    // Back wall
    auto backWall = std::make_shared<Mesh>("back_wall");
    backWall->addQuad(
        Point3(-1.0f, 0.0f, -1.0f), Point3(-1.0f, 2.0f, -1.0f),
        Point3(1.0f, 2.0f, -1.0f), Point3(1.0f, 0.0f, -1.0f)
    );
    // TODO: Implement material assignment per mesh
    scene->addMesh(backWall);
    
    // Left wall (red)
    auto leftWall = std::make_shared<Mesh>("left_wall");
    leftWall->addQuad(
        Point3(-1.0f, 0.0f, 1.0f), Point3(-1.0f, 2.0f, 1.0f),
        Point3(-1.0f, 2.0f, -1.0f), Point3(-1.0f, 0.0f, -1.0f)
    );
    // TODO: Implement material assignment per mesh
    scene->addMesh(leftWall);
    
    // Right wall (green)
    auto rightWall = std::make_shared<Mesh>("right_wall");
    rightWall->addQuad(
        Point3(1.0f, 0.0f, -1.0f), Point3(1.0f, 2.0f, -1.0f),
        Point3(1.0f, 2.0f, 1.0f), Point3(1.0f, 0.0f, 1.0f)
    );
    // TODO: Implement material assignment per mesh
    scene->addMesh(rightWall);
    
    // Area light
    auto areaLight = std::make_shared<Mesh>("area_light");
    areaLight->addQuad(
        Point3(-0.3f, 1.99f, -0.3f), Point3(0.3f, 1.99f, -0.3f),
        Point3(0.3f, 1.99f, 0.3f), Point3(-0.3f, 1.99f, 0.3f)
    );
    // TODO: Implement material assignment per mesh
    scene->addMesh(areaLight);
    
    // Add a point light for testing
    auto pointLight = std::make_shared<PointLight>(Point3(0, 1.8f, 0), Color3(10.0f, 10.0f, 10.0f));
    scene->addLight(pointLight);
    
    std::cout << "Created Cornell Box scene with " << scene->getStatistics().meshCount 
              << " meshes and " << scene->getStatistics().triangleCount << " triangles" << std::endl;
    
    return scene;
}

/**
 * @brief Test basic rendering functionality
 */
void testBasicRendering() {
    std::cout << "\n=== Testing Basic Rendering ===" << std::endl;
    
    try {
        // Create scene
        auto scene = createCornellBoxScene();
        
        // Create camera
        auto camera = std::make_shared<PerspectiveCamera>(
            Point3(0, 1, 3),    // position
            Point3(0, 1, 0),    // target
            Vec3(0, 1, 0),      // up
            45.0f,              // fov
            1.0f                // aspect ratio
        );
        
        // Create integrator (simple normal visualization for now)
        auto integrator = std::make_shared<NormalIntegrator>();
        
        // Create renderer
        auto renderer = std::make_unique<Renderer>();
        
        // Configure render settings
        RenderSettings settings;
        settings.width = 256;
        settings.height = 256;
        settings.samplesPerPixel = 4;
        settings.maxBounces = 3;
        settings.tileSize = 32;
        
        // Setup renderer
        renderer->setScene(scene);
        renderer->setCamera(camera);
        renderer->setIntegrator(integrator);
        renderer->setSettings(settings);
        
        // Set progress callback
        renderer->setProgressCallback([](float progress, const RenderStats& stats) {
            std::cout << "Progress: " << int(progress * 100) << "% - "
                      << stats.renderedTiles << "/" << stats.totalTiles << " tiles" << std::endl;
        });
        
        // Start rendering
        std::cout << "Starting render (" << settings.width << "x" << settings.height 
                  << ", " << settings.samplesPerPixel << " SPP)..." << std::endl;
        
        auto startTime = std::chrono::high_resolution_clock::now();
        
        // Start rendering
        renderer->render();

        // Save result
        std::string outputFile = "test_render.png";
        if (renderer->saveImage(outputFile)) {
            std::cout << "Render saved to: " << outputFile << std::endl;
        }
        
        auto endTime = std::chrono::high_resolution_clock::now();
        auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(endTime - startTime);
        
        std::cout << "Render completed in " << duration.count() << "ms" << std::endl;
        
        // Get final statistics
        const auto& stats = renderer->getStats();
        std::cout << "Total samples: " << stats.totalSamples << std::endl;
        std::cout << "Render time: " << stats.renderTime << "s" << std::endl;
        
    } catch (const std::exception& e) {
        std::cerr << "Error during rendering: " << e.what() << std::endl;
    }
}

/**
 * @brief Test scene loading functionality
 */
void testSceneLoading() {
    std::cout << "\n=== Testing Scene Loading ===" << std::endl;

    // Test creating a test scene
    auto testScene = photon::SceneLoader::createTestScene();
    if (testScene) {
        std::cout << "✓ Test scene created successfully" << std::endl;
    } else {
        std::cout << "✗ Failed to create test scene" << std::endl;
        return;
    }

    // Test loading from JSON file
    photon::SceneLoadStats stats;
    auto loadedScene = photon::SceneLoader::loadFromFile("../scenes/cornell_box.json",
                                                         photon::SceneLoadConfig(), &stats);
    if (loadedScene) {
        std::cout << "✓ Scene loaded from JSON file" << std::endl;
    } else {
        std::cout << "✗ Failed to load scene from JSON file" << std::endl;
    }

    stats.print();

    // Test scene validation
    bool isValid = photon::SceneLoader::validateFile("../scenes/cornell_box.json");
    std::cout << "Scene validation: " << (isValid ? "✓ Valid" : "✗ Invalid") << std::endl;

    // Test saving scene
    if (testScene) {
        bool saved = photon::SceneLoader::saveToFile(*testScene, "test_scene_output.json");
        std::cout << "Scene save test: " << (saved ? "✓ Saved" : "✗ Failed") << std::endl;
    }
}

/**
 * @brief Test mesh loading functionality
 */
void testMeshLoading() {
    std::cout << "\n=== Testing Mesh Loading ===" << std::endl;

    // Test creating test meshes
    auto testCube = photon::MeshLoader::createTestCube();
    if (testCube) {
        std::cout << "✓ Test cube created (" << testCube->getVertices().size() << " vertices, "
                  << testCube->getTriangles().size() << " triangles)" << std::endl;
    }

    auto testSphere = photon::MeshLoader::createTestSphere(1.0f, 8);
    if (testSphere) {
        std::cout << "✓ Test sphere created (" << testSphere->getVertexCount() << " vertices, "
                  << testSphere->getTriangleCount() << " triangles)" << std::endl;
    }

    auto testPlane = photon::MeshLoader::createTestPlane(2.0f, 2.0f, 2);
    if (testPlane) {
        std::cout << "✓ Test plane created (" << testPlane->getVertexCount() << " vertices, "
                  << testPlane->getTriangleCount() << " triangles)" << std::endl;
    }

    // Test loading from OBJ file
    photon::MeshLoadStats stats;
    auto loadedMesh = photon::MeshLoader::loadFromFile("../scenes/test_cube.obj",
                                                       photon::MeshLoadConfig(), &stats);
    if (loadedMesh) {
        std::cout << "✓ Mesh loaded from OBJ file" << std::endl;
    } else {
        std::cout << "✗ Failed to load mesh from OBJ file" << std::endl;
    }

    stats.print();

    // Test mesh validation
    bool isValid = photon::MeshLoader::validateFile("../scenes/test_cube.obj");
    std::cout << "Mesh validation: " << (isValid ? "✓ Valid" : "✗ Invalid") << std::endl;

    // Test saving mesh
    if (testCube) {
        bool saved = photon::MeshLoader::saveToFile(*testCube, "test_cube_output.obj");
        std::cout << "Mesh save test: " << (saved ? "✓ Saved" : "✗ Failed") << std::endl;
    }
}

/**
 * @brief Test math library functionality
 */
void testMathLibrary() {
    std::cout << "\n=== Testing Math Library ===" << std::endl;
    
    // Test Vec3
    Vec3 a(1, 2, 3);
    Vec3 b(4, 5, 6);
    Vec3 c = a + b;
    
    std::cout << "Vec3 test: " << a << " + " << b << " = " << c << std::endl;
    std::cout << "Dot product: " << a.dot(b) << std::endl;
    std::cout << "Cross product: " << a.cross(b) << std::endl;
    std::cout << "Length: " << a.length() << std::endl;
    
    // Test Matrix4
    photon::Matrix4 identity = photon::Matrix4::identity();
    photon::Matrix4 translation = photon::Matrix4::translation(1, 2, 3);
    photon::Matrix4 rotation = photon::Matrix4::rotationY(M_PI / 4);
    
    std::cout << "\nMatrix4 identity:\n" << identity << std::endl;
    std::cout << "Translation matrix:\n" << translation << std::endl;
    
    // Test point transformation
    photon::Point3 point(1, 0, 0);
    photon::Point3 transformed = translation.transformPoint(point);
    std::cout << "Transformed point: " << point << " -> " << transformed << std::endl;

    // Test Ray
    photon::Ray ray(photon::Point3(0, 0, 0), photon::Vec3(1, 0, 0).normalized());
    photon::Point3 rayPoint = ray.at(5.0f);
    std::cout << "Ray at t=5: " << rayPoint << std::endl;
}

/**
 * @brief Main application entry point
 */
int main(int argc, char* argv[]) {
    std::cout << "PhotonRender Test Application" << std::endl;
    std::cout << "Version: " << PHOTON_VERSION_STRING << std::endl;
    std::cout << "=============================" << std::endl;

    // Initialize PhotonRender
    if (!photon::initialize()) {
        std::cerr << "Failed to initialize PhotonRender" << std::endl;
        return 1;
    }

    try {
        // Run comprehensive test suite
        std::cout << "\n=== Running PhotonRender Test Suite ===" << std::endl;
        bool allTestsPassed = photon::PhotonTestSuite::runAllTests();

        if (allTestsPassed) {
            std::cout << "\n🎉 All tests passed! PhotonRender is working correctly." << std::endl;
        } else {
            std::cout << "\n⚠️  Some tests failed. Check the output above for details." << std::endl;
        }

        // Generate test report
        photon::PhotonTestSuite::generateTestReport("test_report.md");
        std::cout << "\nTest report generated: test_report.md" << std::endl;

        // Run individual tests for detailed output
        std::cout << "\n=== Individual Test Details ===" << std::endl;

        // Test math library
        testMathLibrary();

        // Test scene loading
        testSceneLoading();

        // Test mesh loading
        testMeshLoading();

        // Test mock rendering
        std::cout << "\n=== Testing Mock Rendering ===" << std::endl;
        photon::MockRenderer mockRenderer;
        mockRenderer.setResolution(256, 256);
        mockRenderer.setSamples(8);
        mockRenderer.setTestPattern(photon::MockRenderer::TestPattern::CORNELL_BOX);

        mockRenderer.render();
        bool saved = mockRenderer.saveImage("photon_test_render.png");

        const auto& stats = mockRenderer.getStats();
        std::cout << "Mock render completed:" << std::endl;
        std::cout << "  Pixels: " << stats.totalPixels << std::endl;
        std::cout << "  Samples: " << stats.totalSamples << std::endl;
        std::cout << "  Time: " << stats.renderTime << "s" << std::endl;
        std::cout << "  Saved: " << (saved ? "✓" : "✗") << std::endl;

        std::cout << "\n=== All Tests Completed ===" << std::endl;

    } catch (const std::exception& e) {
        std::cerr << "Fatal error: " << e.what() << std::endl;
        photon::shutdown();
        return 1;
    }

    // Shutdown PhotonRender
    photon::shutdown();
    return 0;
}
