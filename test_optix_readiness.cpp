// test_optix_readiness.cpp
// PhotonRender - OptiX Readiness Test
// Test per verificare readiness per integrazione OptiX

#include <iostream>
#include <cuda_runtime.h>
#include <vector>
#include <string>

// Test RT Cores detection
bool testRTCoresAvailability() {
    std::cout << "\n=== RT Cores Detection Test ===" << std::endl;
    
    int device_count;
    cudaError_t error = cudaGetDeviceCount(&device_count);
    
    if (error != cudaSuccess || device_count == 0) {
        std::cerr << "[ERROR] No CUDA devices found" << std::endl;
        return false;
    }
    
    for (int i = 0; i < device_count; i++) {
        cudaDeviceProp prop;
        error = cudaGetDeviceProperties(&prop, i);
        
        if (error != cudaSuccess) {
            std::cerr << "[ERROR] Failed to get device properties for device " << i << std::endl;
            continue;
        }
        
        std::cout << "[INFO] Device " << i << ": " << prop.name << std::endl;
        std::cout << "[INFO] Compute Capability: " << prop.major << "." << prop.minor << std::endl;
        std::cout << "[INFO] Global Memory: " << (prop.totalGlobalMem / 1024 / 1024) << " MB" << std::endl;
        
        // RT Cores sono disponibili da Turing (7.x) in poi
        bool has_rt_cores = prop.major >= 7;
        
        if (has_rt_cores) {
            std::cout << "[SUCCESS] ✅ RT Cores AVAILABLE (Turing/Ampere/Ada architecture)" << std::endl;
            
            // Stima numero RT Cores basata su architettura
            int estimated_rt_cores = 0;
            if (prop.major == 7) {
                // Turing: RTX 20xx series
                estimated_rt_cores = prop.multiProcessorCount; // 1 RT Core per SM
            } else if (prop.major == 8) {
                // Ampere: RTX 30xx series
                estimated_rt_cores = prop.multiProcessorCount; // 1 RT Core per SM
            } else if (prop.major >= 9 || (prop.major == 8 && prop.minor >= 9)) {
                // Ada Lovelace: RTX 40xx series
                estimated_rt_cores = prop.multiProcessorCount; // 1 RT Core per SM
            }
            
            std::cout << "[INFO] Estimated RT Cores: " << estimated_rt_cores << std::endl;
            std::cout << "[INFO] Streaming Multiprocessors: " << prop.multiProcessorCount << std::endl;
            
            return true;
        } else {
            std::cout << "[WARNING] ❌ RT Cores NOT AVAILABLE (Pre-Turing architecture)" << std::endl;
        }
    }
    
    return false;
}

// Test CUDA driver version compatibility
bool testCUDACompatibility() {
    std::cout << "\n=== CUDA Compatibility Test ===" << std::endl;
    
    int driver_version, runtime_version;
    
    cudaError_t error = cudaDriverGetVersion(&driver_version);
    if (error != cudaSuccess) {
        std::cerr << "[ERROR] Failed to get CUDA driver version" << std::endl;
        return false;
    }
    
    error = cudaRuntimeGetVersion(&runtime_version);
    if (error != cudaSuccess) {
        std::cerr << "[ERROR] Failed to get CUDA runtime version" << std::endl;
        return false;
    }
    
    std::cout << "[INFO] CUDA Driver Version: " << driver_version / 1000 << "." << (driver_version % 100) / 10 << std::endl;
    std::cout << "[INFO] CUDA Runtime Version: " << runtime_version / 1000 << "." << (runtime_version % 100) / 10 << std::endl;
    
    // OptiX 7.7 richiede CUDA 11.0+ (driver 450+)
    bool driver_compatible = driver_version >= 11000;
    bool runtime_compatible = runtime_version >= 11000;
    
    if (driver_compatible && runtime_compatible) {
        std::cout << "[SUCCESS] ✅ CUDA version compatible with OptiX 7.7" << std::endl;
        return true;
    } else {
        std::cout << "[ERROR] ❌ CUDA version too old for OptiX 7.7" << std::endl;
        std::cout << "[INFO] Required: CUDA 11.0+ (Driver 450+)" << std::endl;
        return false;
    }
}

// Test memory availability per OptiX
bool testMemoryAvailability() {
    std::cout << "\n=== Memory Availability Test ===" << std::endl;
    
    size_t free_mem, total_mem;
    cudaError_t error = cudaMemGetInfo(&free_mem, &total_mem);
    
    if (error != cudaSuccess) {
        std::cerr << "[ERROR] Failed to get memory info: " << cudaGetErrorString(error) << std::endl;
        return false;
    }
    
    double free_gb = free_mem / (1024.0 * 1024.0 * 1024.0);
    double total_gb = total_mem / (1024.0 * 1024.0 * 1024.0);
    double used_gb = total_gb - free_gb;
    
    std::cout << "[INFO] Total GPU Memory: " << total_gb << " GB" << std::endl;
    std::cout << "[INFO] Used GPU Memory: " << used_gb << " GB" << std::endl;
    std::cout << "[INFO] Free GPU Memory: " << free_gb << " GB" << std::endl;
    
    // OptiX richiede almeno 2GB liberi per scene complesse
    bool memory_sufficient = free_gb >= 2.0;
    
    if (memory_sufficient) {
        std::cout << "[SUCCESS] ✅ Sufficient memory for OptiX rendering" << std::endl;
        
        // Stima capacità scene
        if (free_gb >= 6.0) {
            std::cout << "[INFO] Memory capacity: LARGE scenes (10M+ triangles)" << std::endl;
        } else if (free_gb >= 4.0) {
            std::cout << "[INFO] Memory capacity: MEDIUM scenes (1M+ triangles)" << std::endl;
        } else {
            std::cout << "[INFO] Memory capacity: SMALL scenes (100K+ triangles)" << std::endl;
        }
        
        return true;
    } else {
        std::cout << "[WARNING] ⚠️ Limited memory for OptiX rendering" << std::endl;
        std::cout << "[INFO] Recommended: 2GB+ free GPU memory" << std::endl;
        return false;
    }
}

// Test performance baseline per confronto OptiX
bool testPerformanceBaseline() {
    std::cout << "\n=== Performance Baseline Test ===" << std::endl;
    
    // Test semplice per misurare bandwidth memoria
    const size_t test_size = 100 * 1024 * 1024; // 100MB
    
    float* h_data = new float[test_size / sizeof(float)];
    float* d_data;
    
    // Inizializza dati host
    for (size_t i = 0; i < test_size / sizeof(float); i++) {
        h_data[i] = static_cast<float>(i);
    }
    
    cudaError_t error = cudaMalloc(&d_data, test_size);
    if (error != cudaSuccess) {
        std::cerr << "[ERROR] Failed to allocate GPU memory for test" << std::endl;
        delete[] h_data;
        return false;
    }
    
    // Test bandwidth Host->Device
    cudaEvent_t start, stop;
    cudaEventCreate(&start);
    cudaEventCreate(&stop);
    
    cudaEventRecord(start);
    error = cudaMemcpy(d_data, h_data, test_size, cudaMemcpyHostToDevice);
    cudaEventRecord(stop);
    cudaEventSynchronize(stop);
    
    if (error != cudaSuccess) {
        std::cerr << "[ERROR] Memory copy failed: " << cudaGetErrorString(error) << std::endl;
        cudaFree(d_data);
        delete[] h_data;
        return false;
    }
    
    float h2d_time;
    cudaEventElapsedTime(&h2d_time, start, stop);
    
    // Test bandwidth Device->Host
    cudaEventRecord(start);
    error = cudaMemcpy(h_data, d_data, test_size, cudaMemcpyDeviceToHost);
    cudaEventRecord(stop);
    cudaEventSynchronize(stop);
    
    float d2h_time;
    cudaEventElapsedTime(&d2h_time, start, stop);
    
    // Calcola bandwidth
    double h2d_bandwidth = (test_size / 1024.0 / 1024.0) / (h2d_time / 1000.0);
    double d2h_bandwidth = (test_size / 1024.0 / 1024.0) / (d2h_time / 1000.0);
    
    std::cout << "[INFO] Host->Device Bandwidth: " << h2d_bandwidth << " MB/s" << std::endl;
    std::cout << "[INFO] Device->Host Bandwidth: " << d2h_bandwidth << " MB/s" << std::endl;
    
    // Cleanup
    cudaEventDestroy(start);
    cudaEventDestroy(stop);
    cudaFree(d_data);
    delete[] h_data;
    
    // RTX 4070 dovrebbe avere >400 GB/s bandwidth
    bool bandwidth_good = h2d_bandwidth > 10000; // 10 GB/s minimo
    
    if (bandwidth_good) {
        std::cout << "[SUCCESS] ✅ Good memory bandwidth for OptiX" << std::endl;
        return true;
    } else {
        std::cout << "[WARNING] ⚠️ Low memory bandwidth detected" << std::endl;
        return false;
    }
}

// Test OptiX SDK availability
bool testOptiXSDKAvailability() {
    std::cout << "\n=== OptiX SDK Availability Test ===" << std::endl;
    
    // Percorsi comuni OptiX
    std::vector<std::string> optix_paths = {
        "C:\\ProgramData\\NVIDIA Corporation\\OptiX SDK 7.7.0",
        "C:\\ProgramData\\NVIDIA Corporation\\OptiX SDK 7.6.0",
        "C:\\ProgramData\\NVIDIA Corporation\\OptiX SDK 7.5.0",
        "C:\\Program Files\\NVIDIA Corporation\\OptiX SDK 7.7.0"
    };
    
    bool optix_found = false;
    std::string found_path;
    
    for (const auto& path : optix_paths) {
        // Verifica se il percorso esiste (simulato)
        std::cout << "[INFO] Checking: " << path << std::endl;
        
        // In una implementazione reale, useremmo filesystem API
        // Per ora assumiamo che OptiX non sia installato
    }
    
    if (!optix_found) {
        std::cout << "[WARNING] ❌ OptiX SDK not found" << std::endl;
        std::cout << "[INFO] Download from: https://developer.nvidia.com/optix" << std::endl;
        std::cout << "[INFO] Recommended: OptiX 7.7.0 for RTX 4070" << std::endl;
        return false;
    } else {
        std::cout << "[SUCCESS] ✅ OptiX SDK found at: " << found_path << std::endl;
        return true;
    }
}

int main() {
    std::cout << "=== PhotonRender OptiX Readiness Test ===" << std::endl;
    std::cout << "Testing system readiness for OptiX integration" << std::endl;
    std::cout << "=============================================" << std::endl;
    
    bool all_tests_passed = true;
    
    // Esegui tutti i test
    all_tests_passed &= testRTCoresAvailability();
    all_tests_passed &= testCUDACompatibility();
    all_tests_passed &= testMemoryAvailability();
    all_tests_passed &= testPerformanceBaseline();
    all_tests_passed &= testOptiXSDKAvailability();
    
    std::cout << "\n=== OptiX Readiness Summary ===" << std::endl;
    
    if (all_tests_passed) {
        std::cout << "[SUCCESS] ✅ System READY for OptiX integration!" << std::endl;
        std::cout << "All requirements met for hardware ray tracing." << std::endl;
        std::cout << "Expected performance: 10+ Grays/sec with RT Cores" << std::endl;
    } else {
        std::cout << "[PARTIAL] ⚠️ System PARTIALLY ready for OptiX" << std::endl;
        std::cout << "Some requirements not met. Check warnings above." << std::endl;
        std::cout << "OptiX integration possible but may need adjustments." << std::endl;
    }
    
    std::cout << "\n=== Next Steps ===" << std::endl;
    std::cout << "1. Download OptiX SDK 7.7 from NVIDIA Developer Portal" << std::endl;
    std::cout << "2. Install OptiX SDK to default location" << std::endl;
    std::cout << "3. Configure environment variables" << std::endl;
    std::cout << "4. Compile OptiX integration code" << std::endl;
    std::cout << "5. Run OptiX performance benchmarks" << std::endl;
    
    return all_tests_passed ? 0 : 1;
}
