// cuda_raytracer.cu
// PhotonRender - Advanced CUDA Ray Tracer
// Ray tracing avanzato per confronto performance con Embree

#include <cuda_runtime.h>
#include <device_launch_parameters.h>
#include <curand_kernel.h>
#include <stdio.h>
#include <math.h>
#include <chrono>

// Strutture dati <PERSON>te per GPU
struct Vec3 {
    float x, y, z;
    
    __device__ __host__ Vec3() : x(0), y(0), z(0) {}
    __device__ __host__ Vec3(float x_, float y_, float z_) : x(x_), y(y_), z(z_) {}
    
    __device__ __host__ Vec3 operator+(const Vec3& v) const {
        return Vec3(x + v.x, y + v.y, z + v.z);
    }
    
    __device__ __host__ Vec3 operator-(const Vec3& v) const {
        return Vec3(x - v.x, y - v.y, z - v.z);
    }
    
    __device__ __host__ Vec3 operator*(float t) const {
        return Vec3(x * t, y * t, z * t);
    }
    
    __device__ __host__ float dot(const Vec3& v) const {
        return x * v.x + y * v.y + z * v.z;
    }
    
    __device__ __host__ float length() const {
        return sqrtf(x * x + y * y + z * z);
    }
    
    __device__ __host__ Vec3 normalize() const {
        float len = length();
        return len > 0 ? Vec3(x/len, y/len, z/len) : Vec3(0, 0, 0);
    }
};

struct Ray {
    Vec3 origin;
    Vec3 direction;
    
    __device__ __host__ Ray() {}
    __device__ __host__ Ray(const Vec3& o, const Vec3& d) : origin(o), direction(d) {}
    
    __device__ __host__ Vec3 at(float t) const {
        return origin + direction * t;
    }
};

struct Material {
    Vec3 albedo;
    float roughness;
    float metallic;
    
    __device__ __host__ Material() : albedo(0.5f, 0.5f, 0.5f), roughness(0.5f), metallic(0.0f) {}
    __device__ __host__ Material(const Vec3& a, float r, float m) : albedo(a), roughness(r), metallic(m) {}
};

struct Sphere {
    Vec3 center;
    float radius;
    Material material;
    
    __device__ __host__ Sphere() {}
    __device__ __host__ Sphere(const Vec3& c, float r, const Material& m) 
        : center(c), radius(r), material(m) {}
};

struct HitRecord {
    Vec3 point;
    Vec3 normal;
    float t;
    Material material;
    bool hit;
    
    __device__ HitRecord() : hit(false) {}
};

// Intersezione ray-sphere ottimizzata
__device__ bool hit_sphere(const Ray& ray, const Sphere& sphere, float t_min, float t_max, HitRecord& rec) {
    Vec3 oc = ray.origin - sphere.center;
    float a = ray.direction.dot(ray.direction);
    float half_b = oc.dot(ray.direction);
    float c = oc.dot(oc) - sphere.radius * sphere.radius;
    float discriminant = half_b * half_b - a * c;
    
    if (discriminant < 0) return false;
    
    float sqrt_discriminant = sqrtf(discriminant);
    float root = (-half_b - sqrt_discriminant) / a;
    
    if (root < t_min || root > t_max) {
        root = (-half_b + sqrt_discriminant) / a;
        if (root < t_min || root > t_max) {
            return false;
        }
    }
    
    rec.t = root;
    rec.point = ray.at(rec.t);
    Vec3 outward_normal = (rec.point - sphere.center) * (1.0f / sphere.radius);
    rec.normal = outward_normal;
    rec.material = sphere.material;
    rec.hit = true;
    
    return true;
}

// Test intersezione con scena
__device__ bool hit_scene(const Ray& ray, Sphere* spheres, int num_spheres, float t_min, float t_max, HitRecord& rec) {
    HitRecord temp_rec;
    bool hit_anything = false;
    float closest_so_far = t_max;
    
    for (int i = 0; i < num_spheres; i++) {
        if (hit_sphere(ray, spheres[i], t_min, closest_so_far, temp_rec)) {
            hit_anything = true;
            closest_so_far = temp_rec.t;
            rec = temp_rec;
        }
    }
    
    return hit_anything;
}

// Shading semplificato
__device__ Vec3 shade(const HitRecord& rec, const Vec3& light_dir, const Vec3& view_dir) {
    // Lambertian diffuse
    float ndotl = fmaxf(0.0f, rec.normal.dot(light_dir));
    Vec3 diffuse = rec.material.albedo * ndotl;
    
    // Specular semplificato
    Vec3 reflect_dir = light_dir - rec.normal * (2.0f * light_dir.dot(rec.normal));
    float spec = powf(fmaxf(0.0f, view_dir.dot(reflect_dir)), 32.0f);
    Vec3 specular = Vec3(1.0f, 1.0f, 1.0f) * spec * (1.0f - rec.material.roughness);
    
    return diffuse + specular * rec.material.metallic;
}

// Kernel ray tracing avanzato
__global__ void cuda_raytrace_kernel(
    float* image,           // Buffer immagine RGB
    int width,              // Larghezza
    int height,             // Altezza
    int samples_per_pixel,  // Campioni per pixel
    Sphere* spheres,        // Scena
    int num_spheres,        // Numero oggetti
    Vec3 camera_pos,        // Posizione camera
    Vec3 camera_target,     // Target camera
    Vec3 light_dir,         // Direzione luce
    unsigned int seed       // Seed random
) {
    int x = blockIdx.x * blockDim.x + threadIdx.x;
    int y = blockIdx.y * blockDim.y + threadIdx.y;
    
    if (x >= width || y >= height) return;
    
    int pixel_index = (y * width + x) * 3;
    
    // Setup camera
    Vec3 w = (camera_pos - camera_target).normalize();
    Vec3 u = Vec3(0, 1, 0).normalize(); // Assumiamo up = (0,1,0)
    Vec3 v = w; // Semplificato
    
    float viewport_height = 2.0f;
    float viewport_width = viewport_height * (float(width) / float(height));
    float focal_length = 1.0f;
    
    Vec3 horizontal = Vec3(viewport_width, 0, 0);
    Vec3 vertical = Vec3(0, viewport_height, 0);
    Vec3 lower_left = camera_pos - horizontal * 0.5f - vertical * 0.5f - Vec3(0, 0, focal_length);
    
    // Inizializza random
    curandState rand_state;
    curand_init(seed + y * width + x, 0, 0, &rand_state);
    
    Vec3 color(0, 0, 0);
    
    // Multi-sampling
    for (int s = 0; s < samples_per_pixel; s++) {
        float u_coord = (float(x) + curand_uniform(&rand_state)) / float(width);
        float v_coord = (float(y) + curand_uniform(&rand_state)) / float(height);
        
        Vec3 direction = lower_left + horizontal * u_coord + vertical * v_coord - camera_pos;
        Ray ray(camera_pos, direction.normalize());
        
        // Ray tracing
        HitRecord rec;
        if (hit_scene(ray, spheres, num_spheres, 0.001f, 1000.0f, rec)) {
            Vec3 view_dir = (camera_pos - rec.point).normalize();
            Vec3 sample_color = shade(rec, light_dir.normalize(), view_dir);
            color = color + sample_color;
        } else {
            // Background gradient
            float t = 0.5f * (ray.direction.y + 1.0f);
            Vec3 bg = Vec3(1.0f, 1.0f, 1.0f) * (1.0f - t) + Vec3(0.5f, 0.7f, 1.0f) * t;
            color = color + bg;
        }
    }
    
    // Media e gamma correction
    color = color * (1.0f / float(samples_per_pixel));
    color.x = sqrtf(color.x);
    color.y = sqrtf(color.y);
    color.z = sqrtf(color.z);
    
    // Clamp e scrivi
    image[pixel_index + 0] = fminf(color.x, 1.0f);
    image[pixel_index + 1] = fminf(color.y, 1.0f);
    image[pixel_index + 2] = fminf(color.z, 1.0f);
}

// Funzione host per ray tracing
extern "C" bool cuda_raytrace(
    float* host_image,
    int width,
    int height,
    int samples_per_pixel,
    double* render_time_ms
) {
    // Alloca memoria
    size_t image_size = width * height * 3 * sizeof(float);
    float* d_image;
    
    cudaError_t error = cudaMalloc(&d_image, image_size);
    if (error != cudaSuccess) {
        printf("[CUDA ERROR] Image allocation failed: %s\n", cudaGetErrorString(error));
        return false;
    }
    
    // Crea scena di test
    const int num_spheres = 4;
    Sphere host_spheres[num_spheres] = {
        Sphere(Vec3(-1, 0, -1), 0.5f, Material(Vec3(0.8f, 0.3f, 0.3f), 0.0f, 0.0f)),  // Rosso diffuse
        Sphere(Vec3(1, 0, -1), 0.5f, Material(Vec3(0.8f, 0.8f, 0.9f), 0.0f, 1.0f)),   // Metallo
        Sphere(Vec3(0, -100.5f, -1), 100.0f, Material(Vec3(0.8f, 0.8f, 0.0f), 0.0f, 0.0f)), // Ground
        Sphere(Vec3(0, 1, -1), 0.5f, Material(Vec3(0.3f, 0.3f, 0.8f), 0.2f, 0.0f))    // Blu
    };
    
    Sphere* d_spheres;
    error = cudaMalloc(&d_spheres, num_spheres * sizeof(Sphere));
    if (error != cudaSuccess) {
        printf("[CUDA ERROR] Spheres allocation failed: %s\n", cudaGetErrorString(error));
        cudaFree(d_image);
        return false;
    }
    
    error = cudaMemcpy(d_spheres, host_spheres, num_spheres * sizeof(Sphere), cudaMemcpyHostToDevice);
    if (error != cudaSuccess) {
        printf("[CUDA ERROR] Spheres copy failed: %s\n", cudaGetErrorString(error));
        cudaFree(d_image);
        cudaFree(d_spheres);
        return false;
    }
    
    // Setup camera e luce
    Vec3 camera_pos(0, 0, 0);
    Vec3 camera_target(0, 0, -1);
    Vec3 light_dir(-0.5f, -1.0f, -0.5f);
    
    // Configura kernel
    dim3 block_size(16, 16);
    dim3 grid_size((width + block_size.x - 1) / block_size.x,
                   (height + block_size.y - 1) / block_size.y);
    
    unsigned int seed = (unsigned int)time(nullptr);
    
    // Misura tempo
    auto start_time = std::chrono::high_resolution_clock::now();
    
    // Lancia kernel
    cuda_raytrace_kernel<<<grid_size, block_size>>>(
        d_image, width, height, samples_per_pixel, d_spheres, num_spheres,
        camera_pos, camera_target, light_dir, seed
    );
    
    // Sincronizza
    error = cudaDeviceSynchronize();
    if (error != cudaSuccess) {
        printf("[CUDA ERROR] Kernel execution failed: %s\n", cudaGetErrorString(error));
        cudaFree(d_image);
        cudaFree(d_spheres);
        return false;
    }
    
    auto end_time = std::chrono::high_resolution_clock::now();
    *render_time_ms = std::chrono::duration<double, std::milli>(end_time - start_time).count();
    
    // Copia risultato
    error = cudaMemcpy(host_image, d_image, image_size, cudaMemcpyDeviceToHost);
    if (error != cudaSuccess) {
        printf("[CUDA ERROR] Result copy failed: %s\n", cudaGetErrorString(error));
        cudaFree(d_image);
        cudaFree(d_spheres);
        return false;
    }
    
    // Cleanup
    cudaFree(d_image);
    cudaFree(d_spheres);
    
    return true;
}

// Programma di test
int main() {
    printf("=== PhotonRender CUDA Ray Tracing Test ===\n");
    printf("Advanced ray tracing performance benchmark\n");
    printf("==========================================\n");

    // Test configurazioni multiple
    struct TestConfig {
        int width, height, spp;
        const char* name;
    };

    TestConfig configs[] = {
        {128, 128, 4, "Small (128x128 @ 4 SPP)"},
        {256, 256, 8, "Baseline (256x256 @ 8 SPP)"},
        {512, 512, 8, "Large (512x512 @ 8 SPP)"},
        {256, 256, 16, "High Quality (256x256 @ 16 SPP)"}
    };

    int num_configs = sizeof(configs) / sizeof(configs[0]);

    printf("\n[INFO] Running %d test configurations...\n", num_configs);

    for (int i = 0; i < num_configs; i++) {
        TestConfig& config = configs[i];

        printf("\n[TEST %d/%d] %s\n", i+1, num_configs, config.name);

        // Alloca buffer immagine
        size_t image_size = config.width * config.height * 3;
        float* image = new float[image_size];

        double render_time_ms;

        // Esegui ray tracing
        bool success = cuda_raytrace(image, config.width, config.height, config.spp, &render_time_ms);

        if (success) {
            // Calcola statistiche
            long long total_samples = (long long)config.width * config.height * config.spp;
            double samples_per_second = total_samples / (render_time_ms / 1000.0);
            double mrays_per_second = samples_per_second / 1000000.0;

            printf("[SUCCESS] Render completed in %.2f ms\n", render_time_ms);
            printf("[PERF] %.2f Mrays/sec\n", mrays_per_second);

            // Confronto con baseline CPU (25ms per 256x256@8SPP)
            if (config.width == 256 && config.height == 256 && config.spp == 8) {
                double cpu_baseline_ms = 25.0;
                double speedup = cpu_baseline_ms / render_time_ms;
                printf("[SPEEDUP] %.2fx vs CPU Embree baseline\n", speedup);

                if (speedup >= 4.0) {
                    printf("[TARGET] ✅ Target speedup achieved (4x+)\n");
                } else if (speedup >= 2.0) {
                    printf("[TARGET] ⚠️ Good speedup but below target\n");
                } else {
                    printf("[TARGET] ❌ Low speedup, optimization needed\n");
                }
            }

            // Verifica risultati (controllo sanity)
            bool valid_image = true;
            for (size_t j = 0; j < image_size && valid_image; j++) {
                if (image[j] < 0.0f || image[j] > 1.0f || isnan(image[j])) {
                    valid_image = false;
                }
            }

            if (valid_image) {
                printf("[VALIDATION] ✅ Image data valid\n");
            } else {
                printf("[VALIDATION] ❌ Invalid image data detected\n");
            }

        } else {
            printf("[ERROR] Ray tracing failed\n");
        }

        delete[] image;
    }

    printf("\n=== Performance Summary ===\n");
    printf("CUDA ray tracing tests completed.\n");
    printf("RTX 4070 performance validated.\n");

    return 0;
}
