// test_optix_renderer.cpp
// PhotonRender - OptiX Renderer Test
// Test completo per renderer OptiX con RT Cores

#include <iostream>
#include <chrono>
#include <vector>
#include <algorithm>
#include <cmath>

// USE_OPTIX defined via compiler flag
#include "src/gpu/optix/optix_renderer.h"

using namespace photon::gpu;

// Test basic OptiX initialization
bool test_optix_initialization() {
    std::cout << "\n=== Test OptiX Initialization ===" << std::endl;
    
    OptiXRenderer renderer;
    
    // Test initialization
    OptiXConfig config;
    config.max_trace_depth = 10;
    config.max_primitives = 1000000;
    
    bool success = renderer.initialize(config);
    
    if (success) {
        std::cout << "[SUCCESS] OptiX renderer initialized" << std::endl;
        
        // Test availability
        if (renderer.isAvailable()) {
            std::cout << "[SUCCESS] OptiX renderer is available" << std::endl;
        } else {
            std::cout << "[ERROR] OptiX renderer not available after init" << std::endl;
            return false;
        }
        
        // Get RT Cores info
        int rt_cores = renderer.getRTCoresCount();
        std::cout << "[INFO] RT Cores detected: " << rt_cores << std::endl;
        
        // Get memory info
        size_t memory_mb = renderer.getAvailableMemory();
        std::cout << "[INFO] Available GPU memory: " << memory_mb << " MB" << std::endl;
        
        renderer.shutdown();
        return true;
        
    } else {
        std::cout << "[ERROR] Failed to initialize OptiX renderer" << std::endl;
        return false;
    }
}

// Test OptiX rendering
bool test_optix_rendering() {
    std::cout << "\n=== Test OptiX Rendering ===" << std::endl;
    
    OptiXRenderer renderer;
    
    if (!renderer.initialize()) {
        std::cout << "[ERROR] Failed to initialize renderer for rendering test" << std::endl;
        return false;
    }
    
    // Test configurations
    struct TestConfig {
        int width, height, spp;
        const char* name;
    };
    
    TestConfig configs[] = {
        {256, 256, 8, "Baseline (256x256 @ 8 SPP)"},
        {512, 512, 8, "Large (512x512 @ 8 SPP)"},
        {128, 128, 16, "High Quality (128x128 @ 16 SPP)"},
        {1024, 1024, 4, "4K Preview (1024x1024 @ 4 SPP)"}
    };
    
    int num_configs = sizeof(configs) / sizeof(configs[0]);
    bool all_passed = true;
    
    for (int i = 0; i < num_configs; i++) {
        TestConfig& config = configs[i];
        
        std::cout << "\n[TEST " << (i+1) << "/" << num_configs << "] " << config.name << std::endl;
        
        std::vector<float> image_data;
        
        auto start_time = std::chrono::high_resolution_clock::now();
        bool success = renderer.render(image_data, config.width, config.height, config.spp);
        auto end_time = std::chrono::high_resolution_clock::now();
        
        if (success) {
            auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(end_time - start_time);
            
            // Verify image data
            size_t expected_size = config.width * config.height * 3;
            if (image_data.size() == expected_size) {
                std::cout << "[SUCCESS] Image data size correct: " << expected_size << " floats" << std::endl;
                
                // Check for valid pixel values
                bool valid_pixels = true;
                size_t check_count = (image_data.size() < 100) ? image_data.size() : 100;
                for (size_t j = 0; j < check_count; j++) {
                    float val = image_data[j];
                    if (val < 0.0f || val > 1.0f || (val != val)) { // NaN check
                        valid_pixels = false;
                        break;
                    }
                }
                
                if (valid_pixels) {
                    std::cout << "[SUCCESS] Pixel values valid (0.0-1.0 range)" << std::endl;
                } else {
                    std::cout << "[ERROR] Invalid pixel values detected" << std::endl;
                    all_passed = false;
                }
                
                // Performance analysis
                OptiXStats stats = renderer.getLastStats();
                std::cout << "[PERF] Render time: " << duration.count() << "ms" << std::endl;
                std::cout << "[PERF] Performance: " << (stats.rays_per_second / 1000000.0) << " Mrays/sec" << std::endl;
                std::cout << "[PERF] RT Cores used: " << stats.rt_cores_used << std::endl;
                
                // Compare with CUDA baseline (0.15ms for 256x256@8SPP)
                if (config.width == 256 && config.height == 256 && config.spp == 8) {
                    double cuda_baseline_ms = 0.15;
                    double speedup = cuda_baseline_ms / duration.count();
                    std::cout << "[COMPARISON] vs CUDA baseline: ";
                    if (speedup >= 1.0) {
                        std::cout << speedup << "x FASTER" << std::endl;
                    } else {
                        std::cout << (1.0/speedup) << "x slower" << std::endl;
                    }
                }
                
            } else {
                std::cout << "[ERROR] Image data size mismatch: expected " << expected_size 
                          << ", got " << image_data.size() << std::endl;
                all_passed = false;
            }
            
        } else {
            std::cout << "[ERROR] Rendering failed" << std::endl;
            all_passed = false;
        }
    }
    
    renderer.shutdown();
    return all_passed;
}

// Test OptiX performance vs CUDA
bool test_optix_performance() {
    std::cout << "\n=== Test OptiX Performance Analysis ===" << std::endl;
    
    OptiXRenderer renderer;
    
    if (!renderer.initialize()) {
        std::cout << "[ERROR] Failed to initialize renderer for performance test" << std::endl;
        return false;
    }
    
    // Performance test configuration
    const int width = 256;
    const int height = 256;
    const int spp = 8;
    const int num_runs = 5;
    
    std::cout << "[INFO] Running " << num_runs << " performance tests..." << std::endl;
    std::cout << "[INFO] Configuration: " << width << "x" << height << " @ " << spp << " SPP" << std::endl;
    
    std::vector<double> render_times;
    std::vector<double> performance_values;
    
    for (int run = 0; run < num_runs; run++) {
        std::cout << "\n[RUN " << (run+1) << "/" << num_runs << "]" << std::endl;
        
        std::vector<float> image_data;
        
        auto start_time = std::chrono::high_resolution_clock::now();
        bool success = renderer.render(image_data, width, height, spp);
        auto end_time = std::chrono::high_resolution_clock::now();
        
        if (success) {
            auto duration = std::chrono::duration<double, std::milli>(end_time - start_time);
            double render_time_ms = duration.count();
            
            OptiXStats stats = renderer.getLastStats();
            double mrays_per_sec = stats.rays_per_second / 1000000.0;
            
            render_times.push_back(render_time_ms);
            performance_values.push_back(mrays_per_sec);
            
            std::cout << "[PERF] Time: " << render_time_ms << "ms, Performance: " 
                      << mrays_per_sec << " Mrays/sec" << std::endl;
        } else {
            std::cout << "[ERROR] Run " << (run+1) << " failed" << std::endl;
            return false;
        }
    }
    
    // Calculate statistics
    double avg_time = 0.0, avg_perf = 0.0;
    double min_time = render_times[0], max_time = render_times[0];
    double min_perf = performance_values[0], max_perf = performance_values[0];
    
    for (int i = 0; i < num_runs; i++) {
        avg_time += render_times[i];
        avg_perf += performance_values[i];
        
        min_time = (render_times[i] < min_time) ? render_times[i] : min_time;
        max_time = (render_times[i] > max_time) ? render_times[i] : max_time;
        min_perf = (performance_values[i] < min_perf) ? performance_values[i] : min_perf;
        max_perf = (performance_values[i] > max_perf) ? performance_values[i] : max_perf;
    }
    
    avg_time /= num_runs;
    avg_perf /= num_runs;
    
    std::cout << "\n=== Performance Statistics ===" << std::endl;
    std::cout << "Average Time: " << avg_time << "ms" << std::endl;
    std::cout << "Min Time: " << min_time << "ms" << std::endl;
    std::cout << "Max Time: " << max_time << "ms" << std::endl;
    std::cout << "Average Performance: " << avg_perf << " Mrays/sec" << std::endl;
    std::cout << "Min Performance: " << min_perf << " Mrays/sec" << std::endl;
    std::cout << "Max Performance: " << max_perf << " Mrays/sec" << std::endl;
    
    // Performance targets
    std::cout << "\n=== Performance Targets ===" << std::endl;
    
    // Target: 10+ Grays/sec = 10,000+ Mrays/sec
    double target_grays_per_sec = 10.0;
    double target_mrays_per_sec = target_grays_per_sec * 1000.0;
    
    std::cout << "Target: " << target_grays_per_sec << " Grays/sec (" 
              << target_mrays_per_sec << " Mrays/sec)" << std::endl;
    std::cout << "Achieved: " << (avg_perf / 1000.0) << " Grays/sec (" 
              << avg_perf << " Mrays/sec)" << std::endl;
    
    if (avg_perf >= target_mrays_per_sec) {
        std::cout << "[SUCCESS] ✅ Performance target ACHIEVED!" << std::endl;
    } else {
        double ratio = avg_perf / target_mrays_per_sec;
        std::cout << "[INFO] Performance: " << (ratio * 100.0) << "% of target" << std::endl;
        
        if (ratio >= 0.5) {
            std::cout << "[GOOD] 🟡 Good performance, close to target" << std::endl;
        } else {
            std::cout << "[WARNING] 🔴 Performance below expectations" << std::endl;
        }
    }
    
    // Comparison with CUDA
    double cuda_baseline_mrays = 3521.0; // From previous CUDA tests
    double speedup_vs_cuda = avg_perf / cuda_baseline_mrays;
    
    std::cout << "\n=== Comparison vs CUDA ===" << std::endl;
    std::cout << "CUDA Baseline: " << cuda_baseline_mrays << " Mrays/sec" << std::endl;
    std::cout << "OptiX Performance: " << avg_perf << " Mrays/sec" << std::endl;
    std::cout << "Speedup: " << speedup_vs_cuda << "x" << std::endl;
    
    if (speedup_vs_cuda >= 3.0) {
        std::cout << "[EXCELLENT] 🔥 Significant speedup achieved!" << std::endl;
    } else if (speedup_vs_cuda >= 1.5) {
        std::cout << "[GOOD] ✅ Good speedup achieved" << std::endl;
    } else if (speedup_vs_cuda >= 1.0) {
        std::cout << "[OK] 🟡 Modest improvement" << std::endl;
    } else {
        std::cout << "[WARNING] 🔴 Slower than CUDA baseline" << std::endl;
    }
    
    renderer.shutdown();
    return true;
}

int main() {
    std::cout << "=== PhotonRender OptiX Renderer Test Suite ===" << std::endl;
    std::cout << "Testing OptiX 9.0.0 hardware ray tracing integration" << std::endl;
    std::cout << "====================================================" << std::endl;
    
    bool all_tests_passed = true;
    
    // Run all tests
    all_tests_passed &= test_optix_initialization();
    all_tests_passed &= test_optix_rendering();
    all_tests_passed &= test_optix_performance();
    
    std::cout << "\n=== OptiX Test Suite Summary ===" << std::endl;
    
    if (all_tests_passed) {
        std::cout << "[SUCCESS] ✅ All OptiX tests passed!" << std::endl;
        std::cout << "OptiX 9.0.0 integration is working correctly." << std::endl;
        std::cout << "RT Cores are ready for hardware ray tracing." << std::endl;
        return 0;
    } else {
        std::cout << "[FAILURE] ❌ Some OptiX tests failed." << std::endl;
        std::cout << "OptiX integration needs debugging." << std::endl;
        return 1;
    }
}
