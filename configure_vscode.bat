@echo off
echo Configurazione CMake per VS Code con Visual Studio 2022...

REM Elimina cache CMake esistente
if exist build rmdir /s /q build
if exist build-debug rmdir /s /q build-debug

REM Crea directory build
mkdir build
cd build

REM Configura con Visual Studio 2022
cmake -G "Visual Studio 17 2022" -A x64 ^
      -DCMAKE_EXPORT_COMPILE_COMMANDS=TRUE ^
      -DBUILD_BENCHMARKS=OFF ^
      -DUSE_CUDA=OFF ^
      -DBUILD_TESTS=ON ^
      ..

if %ERRORLEVEL% EQU 0 (
    echo.
    echo ✅ Configurazione completata con successo!
    echo ✅ Visual Studio 2022 configurato correttamente
    echo.
    echo Ora puoi:
    echo 1. Riavviare VS Code
    echo 2. Aprire Command Palette (Ctrl+Shift+P)
    echo 3. Eseguire "CMake: Select Kit" e scegliere "Visual Studio Community 2022 Release - amd64"
    echo 4. Eseguire "CMake: Select Variant" e scegliere "Release"
    echo.
) else (
    echo ❌ Errore durante la configurazione
    pause
)

cd ..
pause
