@echo off
echo === PhotonRender OptiX Readiness Test ===

REM Change to project directory
cd /d "C:\xampp\htdocs\progetti\photon-render"

REM Setup Visual Studio environment
call "C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Auxiliary\Build\vcvars64.bat"

echo.
echo [INFO] Compiling OptiX readiness test...
nvcc -o test_optix_readiness.exe test_optix_readiness.cpp -arch=sm_89 -O3 -std=c++17

if %ERRORLEVEL% EQU 0 (
    echo [SUCCESS] OptiX readiness test compilation successful
    echo.
    echo [INFO] Running OptiX readiness tests...
    echo.
    test_optix_readiness.exe
    echo.
    echo [INFO] OptiX readiness tests completed
) else (
    echo [ERROR] OptiX readiness test compilation failed
    exit /b 1
)

echo.
echo [INFO] OptiX readiness assessment completed
pause
