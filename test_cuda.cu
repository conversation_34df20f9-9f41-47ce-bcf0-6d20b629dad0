// test_cuda.cu
// Simple CUDA test to verify compilation and runtime

#include <cuda_runtime.h>
#include <iostream>

__global__ void testKernel(float* data, int n) {
    int idx = blockIdx.x * blockDim.x + threadIdx.x;
    if (idx < n) {
        data[idx] = idx * 2.0f;
    }
}

int main() {
    std::cout << "CUDA Test Application" << std::endl;
    
    // Check CUDA device
    int deviceCount;
    cudaError_t error = cudaGetDeviceCount(&deviceCount);
    
    if (error != cudaSuccess) {
        std::cerr << "CUDA Error: " << cudaGetErrorString(error) << std::endl;
        return 1;
    }
    
    std::cout << "Found " << deviceCount << " CUDA device(s)" << std::endl;
    
    for (int i = 0; i < deviceCount; i++) {
        cudaDeviceProp prop;
        cudaGetDeviceProperties(&prop, i);
        
        std::cout << "\nDevice " << i << ": " << prop.name << std::endl;
        std::cout << "  Compute Capability: " << prop.major << "." << prop.minor << std::endl;
        std::cout << "  Total Memory: " << prop.totalGlobalMem / (1024*1024) << " MB" << std::endl;
        std::cout << "  Multiprocessors: " << prop.multiProcessorCount << std::endl;
        std::cout << "  Max Threads per Block: " << prop.maxThreadsPerBlock << std::endl;
        std::cout << "  RT Cores: " << (prop.major >= 7 ? "Yes" : "No") << std::endl;
    }
    
    // Test simple kernel
    const int n = 1024;
    float* h_data = new float[n];
    float* d_data;
    
    // Allocate GPU memory
    cudaMalloc(&d_data, n * sizeof(float));
    
    // Launch kernel
    int blockSize = 256;
    int gridSize = (n + blockSize - 1) / blockSize;
    testKernel<<<gridSize, blockSize>>>(d_data, n);
    
    // Copy back
    cudaMemcpy(h_data, d_data, n * sizeof(float), cudaMemcpyDeviceToHost);
    
    // Verify results
    bool success = true;
    for (int i = 0; i < 10; i++) {
        if (h_data[i] != i * 2.0f) {
            success = false;
            break;
        }
    }
    
    std::cout << "\nKernel Test: " << (success ? "PASSED" : "FAILED") << std::endl;
    
    // Cleanup
    cudaFree(d_data);
    delete[] h_data;
    
    std::cout << "CUDA test completed successfully!" << std::endl;
    return 0;
}
