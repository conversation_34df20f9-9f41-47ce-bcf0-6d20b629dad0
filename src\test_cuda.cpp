// src/test_cuda.cpp
// PhotonRender - CUDA Test Application
// Test per verificare funzionamento CUDA integration

#include <iostream>
#include <chrono>
#include <vector>
#include "gpu/cuda/cuda_integration.h"

int main() {
    std::cout << "=== PhotonRender CUDA Test ===" << std::endl;
    std::cout << "Testing CUDA integration and performance" << std::endl;
    std::cout << "=======================================" << std::endl;
    
    // Crea renderer CUDA
    photon::gpu::CudaRenderer cuda_renderer;
    
    // Test inizializzazione
    std::cout << "\n[TEST] Initializing CUDA..." << std::endl;
    if (!cuda_renderer.initialize()) {
        std::cerr << "[ERROR] Failed to initialize CUDA renderer" << std::endl;
        return 1;
    }
    
    if (!cuda_renderer.isAvailable()) {
        std::cerr << "[ERROR] CUDA renderer not available" << std::endl;
        return 1;
    }
    
    std::cout << "[SUCCESS] CUDA renderer initialized" << std::endl;
    
    // Test rendering piccolo (128x128)
    std::cout << "\n[TEST] Small render test (128x128 @ 4 SPP)..." << std::endl;
    std::vector<float> small_image;
    
    auto start_time = std::chrono::high_resolution_clock::now();
    bool success = cuda_renderer.renderTestScene(small_image, 128, 128, 4);
    auto end_time = std::chrono::high_resolution_clock::now();
    
    if (!success) {
        std::cerr << "[ERROR] Small render test failed" << std::endl;
        return 1;
    }
    
    auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(end_time - start_time);
    std::cout << "[SUCCESS] Small render completed in " << duration.count() << "ms" << std::endl;
    
    // Test rendering medio (256x256) - baseline comparison
    std::cout << "\n[TEST] Baseline render test (256x256 @ 8 SPP)..." << std::endl;
    std::vector<float> baseline_image;
    
    start_time = std::chrono::high_resolution_clock::now();
    success = cuda_renderer.renderTestScene(baseline_image, 256, 256, 8);
    end_time = std::chrono::high_resolution_clock::now();
    
    if (!success) {
        std::cerr << "[ERROR] Baseline render test failed" << std::endl;
        return 1;
    }
    
    duration = std::chrono::duration_cast<std::chrono::milliseconds>(end_time - start_time);
    std::cout << "[SUCCESS] Baseline render completed in " << duration.count() << "ms" << std::endl;
    
    // Calcola performance
    long long total_samples = 256LL * 256 * 8;
    double samples_per_second = total_samples / (duration.count() / 1000.0);
    double mrays_per_second = samples_per_second / 1000000.0;
    
    std::cout << "\n=== PERFORMANCE RESULTS ===" << std::endl;
    std::cout << "Resolution: 256x256" << std::endl;
    std::cout << "Samples per pixel: 8" << std::endl;
    std::cout << "Total samples: " << total_samples << std::endl;
    std::cout << "Render time: " << duration.count() << "ms" << std::endl;
    std::cout << "Performance: " << mrays_per_second << " Mrays/sec" << std::endl;
    
    // Confronto con baseline CPU (25ms target)
    double cpu_baseline_ms = 25.0;
    double speedup = cpu_baseline_ms / duration.count();
    
    std::cout << "\n=== SPEEDUP ANALYSIS ===" << std::endl;
    std::cout << "CPU baseline (Embree): " << cpu_baseline_ms << "ms" << std::endl;
    std::cout << "GPU CUDA: " << duration.count() << "ms" << std::endl;
    std::cout << "Speedup: " << speedup << "x" << std::endl;
    
    if (speedup >= 4.0) {
        std::cout << "[SUCCESS] Target speedup achieved (4x+)" << std::endl;
    } else if (speedup >= 2.0) {
        std::cout << "[PARTIAL] Good speedup but below target (2x+)" << std::endl;
    } else {
        std::cout << "[WARNING] Low speedup, optimization needed" << std::endl;
    }
    
    // Test rendering grande (512x512)
    std::cout << "\n[TEST] Large render test (512x512 @ 8 SPP)..." << std::endl;
    std::vector<float> large_image;
    
    start_time = std::chrono::high_resolution_clock::now();
    success = cuda_renderer.renderTestScene(large_image, 512, 512, 8);
    end_time = std::chrono::high_resolution_clock::now();
    
    if (!success) {
        std::cerr << "[ERROR] Large render test failed" << std::endl;
        return 1;
    }
    
    duration = std::chrono::duration_cast<std::chrono::milliseconds>(end_time - start_time);
    std::cout << "[SUCCESS] Large render completed in " << duration.count() << "ms" << std::endl;
    
    // Test salvataggio immagine (placeholder)
    std::cout << "\n[TEST] Image save test..." << std::endl;
    success = cuda_renderer.saveImagePNG(baseline_image, 256, 256, "cuda_test_output.png");
    if (success) {
        std::cout << "[SUCCESS] Image save test completed" << std::endl;
    } else {
        std::cout << "[WARNING] Image save test failed (expected - needs STB integration)" << std::endl;
    }
    
    // Cleanup
    std::cout << "\n[TEST] Cleanup..." << std::endl;
    cuda_renderer.shutdown();
    std::cout << "[SUCCESS] CUDA renderer shutdown complete" << std::endl;
    
    std::cout << "\n=== ALL TESTS COMPLETED ===" << std::endl;
    std::cout << "CUDA integration is working correctly!" << std::endl;
    
    return 0;
}
