// test_memory_optimization.cpp
// PhotonRender - Memory Optimization Test
// Test completo per memory manager e ottimizzazioni GPU

#include <iostream>
#include <chrono>
#include <vector>
#include <memory>
#include "src/gpu/cuda/cuda_memory_manager.h"

using namespace photon::gpu;

// Test memory manager base
bool test_memory_manager_basic() {
    std::cout << "\n=== Test Memory Manager Basic ===" << std::endl;
    
    auto& manager = CudaMemoryManager::getInstance();
    
    // Inizializza con 128MB
    if (!manager.initialize(128 * 1024 * 1024)) {
        std::cerr << "[ERROR] Failed to initialize memory manager" << std::endl;
        return false;
    }
    
    std::cout << "[SUCCESS] Memory manager initialized" << std::endl;
    
    // Test allocazioni multiple
    std::vector<void*> allocations;
    
    for (int i = 0; i < 10; i++) {
        size_t size = (i + 1) * 1024 * 1024; // 1MB, 2MB, 3MB, etc.
        void* ptr = manager.allocate(size);
        
        if (ptr) {
            allocations.push_back(ptr);
            std::cout << "[SUCCESS] Allocated " << size / 1024 / 1024 << " MB" << std::endl;
        } else {
            std::cout << "[ERROR] Failed to allocate " << size / 1024 / 1024 << " MB" << std::endl;
            return false;
        }
    }
    
    // Stampa statistiche
    manager.printStats();
    
    // Dealloca tutto
    for (void* ptr : allocations) {
        manager.deallocate(ptr);
    }
    
    std::cout << "\n[INFO] After deallocation:" << std::endl;
    manager.printStats();
    
    manager.shutdown();
    return true;
}

// Test RAII buffers
bool test_cuda_buffers() {
    std::cout << "\n=== Test CUDA Buffers (RAII) ===" << std::endl;
    
    auto& manager = CudaMemoryManager::getInstance();
    if (!manager.initialize(64 * 1024 * 1024)) {
        return false;
    }
    
    {
        // Test FloatBuffer
        FloatBuffer image_buffer(1024 * 1024); // 1M floats
        if (image_buffer.empty()) {
            std::cerr << "[ERROR] Failed to create image buffer" << std::endl;
            return false;
        }
        
        std::cout << "[SUCCESS] Created image buffer: " << image_buffer.size() << " floats" << std::endl;
        
        // Test data copy
        std::vector<float> host_data(1024, 1.0f);
        cudaError_t error = image_buffer.copyFromHost(host_data.data(), host_data.size());
        if (error != cudaSuccess) {
            std::cerr << "[ERROR] Failed to copy to device: " << cudaGetErrorString(error) << std::endl;
            return false;
        }
        
        std::cout << "[SUCCESS] Copied data to device" << std::endl;
        
        // Test Vec3Buffer
        Vec3Buffer geometry_buffer(10000); // 10K vec3
        if (!geometry_buffer.empty()) {
            std::cout << "[SUCCESS] Created geometry buffer: " << geometry_buffer.size() << " vec3s" << std::endl;
        }
        
        manager.printStats();
        
    } // Buffers automaticamente deallocati qui
    
    std::cout << "\n[INFO] After automatic deallocation:" << std::endl;
    manager.printStats();
    
    manager.shutdown();
    return true;
}

// Test performance allocazioni
bool test_allocation_performance() {
    std::cout << "\n=== Test Allocation Performance ===" << std::endl;
    
    auto& manager = CudaMemoryManager::getInstance();
    if (!manager.initialize(256 * 1024 * 1024)) {
        return false;
    }
    
    const int num_allocations = 1000;
    const size_t allocation_size = 1024 * 1024; // 1MB each
    
    std::vector<void*> ptrs;
    ptrs.reserve(num_allocations);
    
    // Test allocazioni con memory manager
    auto start_time = std::chrono::high_resolution_clock::now();
    
    for (int i = 0; i < num_allocations; i++) {
        void* ptr = manager.allocate(allocation_size);
        if (ptr) {
            ptrs.push_back(ptr);
        }
    }
    
    auto end_time = std::chrono::high_resolution_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::microseconds>(end_time - start_time);
    
    std::cout << "[PERF] Memory Manager: " << num_allocations << " allocations in " 
              << duration.count() << " μs" << std::endl;
    std::cout << "[PERF] Average: " << (duration.count() / num_allocations) << " μs per allocation" << std::endl;
    
    manager.printStats();
    
    // Dealloca tutto
    start_time = std::chrono::high_resolution_clock::now();
    
    for (void* ptr : ptrs) {
        manager.deallocate(ptr);
    }
    
    end_time = std::chrono::high_resolution_clock::now();
    duration = std::chrono::duration_cast<std::chrono::microseconds>(end_time - start_time);
    
    std::cout << "[PERF] Deallocations: " << duration.count() << " μs total" << std::endl;
    
    // Confronto con allocazioni dirette CUDA
    ptrs.clear();
    
    start_time = std::chrono::high_resolution_clock::now();
    
    for (int i = 0; i < 100; i++) { // Meno allocazioni per evitare out of memory
        void* ptr;
        cudaError_t error = cudaMalloc(&ptr, allocation_size);
        if (error == cudaSuccess) {
            ptrs.push_back(ptr);
        }
    }
    
    end_time = std::chrono::high_resolution_clock::now();
    duration = std::chrono::duration_cast<std::chrono::microseconds>(end_time - start_time);
    
    std::cout << "[PERF] Direct CUDA: 100 allocations in " << duration.count() << " μs" << std::endl;
    std::cout << "[PERF] Average: " << (duration.count() / 100) << " μs per allocation" << std::endl;
    
    // Cleanup direct allocations
    for (void* ptr : ptrs) {
        cudaFree(ptr);
    }
    
    manager.shutdown();
    return true;
}

// Test texture management
bool test_texture_management() {
    std::cout << "\n=== Test Texture Management ===" << std::endl;
    
    auto& manager = CudaMemoryManager::getInstance();
    if (!manager.initialize(128 * 1024 * 1024)) {
        return false;
    }
    
    // Test allocazione texture 2D
    cudaChannelFormatDesc format = cudaCreateChannelDesc<float4>();
    
    std::vector<cudaArray_t> textures;
    
    // Alloca multiple texture
    for (int i = 0; i < 5; i++) {
        int size = 256 << i; // 256, 512, 1024, 2048, 4096
        
        cudaArray_t texture = manager.allocateTexture2D(size, size, format);
        if (texture) {
            textures.push_back(texture);
            std::cout << "[SUCCESS] Allocated " << size << "x" << size << " texture" << std::endl;
        } else {
            std::cout << "[ERROR] Failed to allocate " << size << "x" << size << " texture" << std::endl;
        }
    }
    
    manager.printStats();
    
    // Dealloca texture
    for (cudaArray_t texture : textures) {
        manager.deallocateTexture(texture);
    }
    
    std::cout << "\n[INFO] After texture deallocation:" << std::endl;
    manager.printStats();
    
    manager.shutdown();
    return true;
}

// Test streaming memory
bool test_memory_streaming() {
    std::cout << "\n=== Test Memory Streaming ===" << std::endl;
    
    auto& manager = CudaMemoryManager::getInstance();
    if (!manager.initialize(64 * 1024 * 1024)) {
        return false;
    }
    
    const size_t data_size = 10 * 1024 * 1024; // 10MB
    
    // Alloca host e device memory
    std::vector<float> host_data(data_size / sizeof(float), 3.14159f);
    void* device_ptr = manager.allocate(data_size);
    
    if (!device_ptr) {
        std::cerr << "[ERROR] Failed to allocate device memory" << std::endl;
        return false;
    }
    
    // Test streaming to device
    auto start_time = std::chrono::high_resolution_clock::now();
    
    bool success = manager.streamToDevice(device_ptr, host_data.data(), data_size);
    
    auto end_time = std::chrono::high_resolution_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::microseconds>(end_time - start_time);
    
    if (success) {
        double bandwidth = (data_size / 1024.0 / 1024.0) / (duration.count() / 1000000.0);
        std::cout << "[SUCCESS] Streamed " << (data_size / 1024 / 1024) << " MB to device in " 
                  << duration.count() << " μs" << std::endl;
        std::cout << "[PERF] Bandwidth: " << bandwidth << " MB/s" << std::endl;
    } else {
        std::cout << "[ERROR] Failed to stream to device" << std::endl;
    }
    
    // Test streaming from device
    std::vector<float> result_data(data_size / sizeof(float));
    
    start_time = std::chrono::high_resolution_clock::now();
    
    success = manager.streamFromDevice(result_data.data(), device_ptr, data_size);
    
    end_time = std::chrono::high_resolution_clock::now();
    duration = std::chrono::duration_cast<std::chrono::microseconds>(end_time - start_time);
    
    if (success) {
        double bandwidth = (data_size / 1024.0 / 1024.0) / (duration.count() / 1000000.0);
        std::cout << "[SUCCESS] Streamed " << (data_size / 1024 / 1024) << " MB from device in " 
                  << duration.count() << " μs" << std::endl;
        std::cout << "[PERF] Bandwidth: " << bandwidth << " MB/s" << std::endl;
        
        // Verifica dati
        bool data_valid = true;
        for (size_t i = 0; i < 100; i++) { // Check first 100 elements
            if (std::abs(result_data[i] - 3.14159f) > 1e-6f) {
                data_valid = false;
                break;
            }
        }
        
        if (data_valid) {
            std::cout << "[SUCCESS] Data integrity verified" << std::endl;
        } else {
            std::cout << "[ERROR] Data corruption detected" << std::endl;
        }
    } else {
        std::cout << "[ERROR] Failed to stream from device" << std::endl;
    }
    
    manager.deallocate(device_ptr);
    manager.shutdown();
    return true;
}

int main() {
    std::cout << "=== PhotonRender Memory Optimization Test Suite ===" << std::endl;
    std::cout << "Testing advanced GPU memory management features" << std::endl;
    std::cout << "=================================================" << std::endl;
    
    bool all_tests_passed = true;
    
    // Esegui tutti i test
    all_tests_passed &= test_memory_manager_basic();
    all_tests_passed &= test_cuda_buffers();
    all_tests_passed &= test_allocation_performance();
    all_tests_passed &= test_texture_management();
    all_tests_passed &= test_memory_streaming();
    
    std::cout << "\n=== Test Suite Summary ===" << std::endl;
    if (all_tests_passed) {
        std::cout << "[SUCCESS] All memory optimization tests passed!" << std::endl;
        std::cout << "GPU memory management is working correctly." << std::endl;
        return 0;
    } else {
        std::cout << "[FAILURE] Some tests failed." << std::endl;
        std::cout << "Memory optimization needs debugging." << std::endl;
        return 1;
    }
}
