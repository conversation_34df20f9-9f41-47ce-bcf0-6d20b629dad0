@echo off
echo === PhotonRender CUDA Ray Tracer Test ===

REM Change to project directory
cd /d "C:\xampp\htdocs\progetti\photon-render"

REM Setup Visual Studio environment
call "C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Auxiliary\Build\vcvars64.bat"

echo.
echo [INFO] Compiling CUDA ray tracer...
nvcc -o cuda_raytracer.exe cuda_raytracer.cu -arch=sm_89 -O3 --use_fast_math

if %ERRORLEVEL% EQU 0 (
    echo [SUCCESS] CUDA ray tracer compilation successful
    echo.
    echo [INFO] Running ray tracing performance tests...
    echo.
    cuda_raytracer.exe
) else (
    echo [ERROR] CUDA ray tracer compilation failed
    exit /b 1
)

echo.
echo [INFO] Ray tracing tests completed
pause
