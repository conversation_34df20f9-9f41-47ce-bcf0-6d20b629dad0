@echo off
REM PhotonRender Simplified Build Script
REM This script builds and tests PhotonRender without Embree dependencies

echo ========================================
echo PhotonRender Simplified Build
echo ========================================
echo.

REM Create build directory
if not exist "build_simple" (
    echo Creating build directory...
    mkdir build_simple
)

REM Copy simplified CMakeLists.txt
echo Copying simplified CMake configuration...
copy CMakeLists_simple.txt build_simple\CMakeLists.txt

cd build_simple

REM Configure with simplified CMake
echo Configuring simplified build...
cmake . -G "Visual Studio 17 2022" -A x64 -DBUILD_TESTS=ON -DBUILD_SIMPLE=ON

if %ERRORLEVEL% NEQ 0 (
    echo Configuration failed!
    pause
    exit /b 1
)

REM Build the project
echo.
echo Building PhotonRender simplified...
cmake --build . --config Release --target photon_test_simple

if %ERRORLEVEL% NEQ 0 (
    echo Build failed!
    pause
    exit /b 1
)

echo.
echo ========================================
echo Build successful!
echo ========================================
echo.

REM Run tests
echo Running PhotonRender tests...
echo.

if exist "bin\Release\photon_test_simple.exe" (
    bin\Release\photon_test_simple.exe
) else (
    echo Test executable not found!
    pause
    exit /b 1
)

echo.
echo ========================================
echo Tests completed!
echo ========================================
echo.

REM Show generated files
echo Generated files:
if exist "test_report.md" (
    echo   - test_report.md (Test report)
)
if exist "photon_test_render.png" (
    echo   - photon_test_render.png (Test render)
)
if exist "test_image_io.png" (
    echo   - test_image_io.png (Image I/O test)
)
if exist "test_mock_render.png" (
    echo   - test_mock_render.png (Mock render test)
)

echo.
echo Build and test completed successfully!
pause
