@echo off
echo === PhotonRender Memory Optimization Test ===

REM Change to project directory
cd /d "C:\xampp\htdocs\progetti\photon-render"

REM Setup Visual Studio environment
call "C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Auxiliary\Build\vcvars64.bat"

echo.
echo [INFO] Compiling memory manager...
nvcc -c src/gpu/cuda/cuda_memory_manager.cpp -o cuda_memory_manager.obj -arch=sm_89 -O3

if %ERRORLEVEL% NEQ 0 (
    echo [ERROR] Memory manager compilation failed
    exit /b 1
)

echo [SUCCESS] Memory manager compiled

echo.
echo [INFO] Compiling optimized renderer...
nvcc -o cuda_renderer_optimized.exe src/gpu/cuda/cuda_renderer_optimized.cu -arch=sm_89 -O3 --use_fast_math

if %ERRORLEVEL% EQU 0 (
    echo [SUCCESS] Optimized renderer compilation successful
    echo.
    echo [INFO] Running optimized renderer tests...
    echo.
    cuda_renderer_optimized.exe
    echo.
    echo [INFO] Optimized renderer tests completed
) else (
    echo [ERROR] Optimized renderer compilation failed
    echo [INFO] Continuing with memory manager tests...
)

echo.
echo [INFO] Compiling memory optimization test...
nvcc -o test_memory_optimization.exe test_memory_optimization.cpp cuda_memory_manager.obj -arch=sm_89 -O3

if %ERRORLEVEL% EQU 0 (
    echo [SUCCESS] Memory test compilation successful
    echo.
    echo [INFO] Running memory optimization tests...
    echo.
    test_memory_optimization.exe
) else (
    echo [ERROR] Memory test compilation failed
)

echo.
echo [INFO] Memory optimization tests completed
pause
